/**
 * Import function triggers from their respective submodules:
 *
 * const {onCall} = require("firebase-functions/v2/https");
 * const {onDocumentWritten} = require("firebase-functions/v2/firestore");
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */
const { onCall } = require("firebase-functions/v2/https");
const { onRequest } = require("firebase-functions/v2/https");
const { setGlobalOptions } = require("firebase-functions/v2");
const logger = require("firebase-functions/logger");
const stripe = require('stripe')('sk_test_51LqGF2Gp70OVnyZVU3f1D4zJ7xHHZpDOWU9eufzVywPbb8U8etAwTy3jP99bWWKwfqwxGnBFKWeHEjE5zhmlyayr00oRaw2d7D'); // Test
// const stripe = require('stripe')('***********************************************************************************************************'); // Production
const endpointSecret = "whsec_YDqxmwptTVItay5dXGy6RWc6U04x9PZR"; // Test
// const endpointSecret = "whsec_KR7Yam4rdPKDWU97ckWc5Bt82KWrcjhD"; // Production
const admin = require("firebase-admin");
const dateFuncs = require('date-and-time');
admin.initializeApp();
const db = admin.firestore();
const auth = admin.auth();

setGlobalOptions({ maxInstances: 10 });

exports.checkout = onCall(async (request) => {
    console.log(request.data);
    const session = await stripe.checkout.sessions.create({
        line_items: request.data.list,
        client_reference_id: request.data.orderId,
        customer_email: request.data.email,
        mode: 'payment',
        success_url: 'http://localhost:4242/success',
        cancel_url: 'http://localhost:4242/cancel',
    });
    logger.info(session, { structuredData: true });
    return { "success": true, "msg": session.url };
});

exports.success = onRequest(async (request, response) => {
    const sig = request.headers['stripe-signature'];

    let event;
    try {
        event = stripe.webhooks.constructEvent(request.rawBody, sig, endpointSecret);
    } catch (err) {
        console.log(err);
        response.status(400).send(`Webhook Error: ${err.message}`);
        return;
    }

    // Handle the event
    switch (event.type) {
        case 'checkout.session.completed':
            try {
                console.log("INSIDE: checkout.session.completed");
                const checkoutSessionCompleted = event.data.object;
                const refId = checkoutSessionCompleted['client_reference_id']; //"G7aZkGsKbxODh5bLt2UC";
                const orderDocRef = db.collection("orders").doc(refId);
                // Transaction
                const orderDoc = await db.runTransaction(async trans => {
                    const orderDoc = await trans.get(orderDocRef);
                    const processed = orderDoc.data()['processed'];
                    console.log(`Processed:: ${processed}`);
                    if (processed === false) {
                        await trans.update(orderDocRef, { "processed": true });
                        return orderDoc;
                    }
                    return null;
                });
                console.log(`Order Completed: ${orderDoc}`);
                // Fetch Order Doc 
                // const orderDoc = await orderDocRef.get();
                if (orderDoc == null) {
                    return response.end();
                }
                // Fetch User Doc
                const userDoc = await db.collection('users').doc(orderDoc.data()['uid']).get();
                const isEnterprise = userDoc.data()['eId'] != null;
                const batch = db.batch()
                let totalSaleVal = 0;
                let totalCrseQty = 0;
                for (var i = 0; i < orderDoc.data()['checkoutCourses'].length; i++) {
                    const courseDoc = await db.collection('courses')
                        .doc(orderDoc.data()['checkoutCourses'][i]['courseId'])
                        .get();
                    let qtySold = orderDoc.data()['checkoutCourses'][i]['qty'];
                    let saleValue = isEnterprise
                        ? (qtySold >= courseDoc.data()['bulkMinQty']
                            ? courseDoc.data()['bulkPrice'] * qtySold
                            : courseDoc.data()['price'] * qtySold)
                        : courseDoc.data()['price'];
                    // Updating Course flags
                    batch.update(db.collection('courses').doc(courseDoc.id), {
                        "qtySold": admin.firestore.FieldValue.increment(qtySold),
                        "valueSold": admin.firestore.FieldValue.increment(saleValue),
                    });
                    totalSaleVal += saleValue;
                    totalCrseQty += qtySold;
                    // Create User Course Document
                    const start = new Date();
                    const end = dateFuncs.addDays(start, courseDoc.data()['days']);
                    batch.create(db.collection('userCourses').doc(), {
                        "courseId": courseDoc.id,
                        "isEnterprise": isEnterprise,
                        "uid": userDoc.id,
                        "orderId": refId,
                        "assignedByUid": null,
                        "assignedByName": null,
                        "blocked": false,
                        "startDate": start,//DateTime.now(),
                        "endDate": end,//DateTime.now().add(Duration(days: courseDoc['days'])),
                        "examSchedules": {},
                        "certiUrl": null,
                        "score": null,
                        "progress": {},
                        "asignScores": {},
                        "qty": orderDoc.data()['checkoutCourses'][i]['qty'],
                        "assigned": 0,
                        "completed": false,
                        "time": admin.firestore.FieldValue.serverTimestamp(),
                    });
                }
                // Updating Users Document
                let userData = orderDoc.data()['fromCart'] == true ? {
                    "cartItems": {},
                    "qtyPurchased": admin.firestore.FieldValue.increment(totalCrseQty),
                    "valuePurchased": admin.firestore.FieldValue.increment(totalSaleVal),
                } : {
                    "qtyPurchased": admin.firestore.FieldValue.increment(totalCrseQty),
                    "valuePurchased": admin.firestore.FieldValue.increment(totalSaleVal),
                };
                batch.update(db.collection('users').doc(userDoc.id), userData);
                await batch.commit();
            } catch (error) {
                console.log(error);
            }
            break;
        case 'payment_intent.succeeded':
            break;
        default:
            console.log(`Unhandled event type ${event.type}`);
    }

    // Return a 200 response to acknowledge receipt of the event
    response.send();
});

// exports.createUser = onRequest(async (req, res) => {
exports.createUser = onCall(async (request) => {
    // let data = req.body;
    try {
        if (!request.auth) return { status: 'error', code: 401, message: 'Not signed in' };
        logger.info(request.data);
        let pass = Math.random().toString(36).slice(-8);
        let response1 = await auth.createUser({
            email: request.data.email,
            password: pass,
        });
        console.log('Successfully created new user:', response1.uid);
        try {
            let response2 = await db.collection('users').doc(response1.uid).create({
                "createdBy": request.data.eUid,
                "name": request.data.name,
                "contact": request.data.phone,
                "email": request.data.email,
                "eId": null,
                "branch": request.data.branch,
                "bIds": { [request.data.bId]: true },
                "linkedWith": { [request.data.eId]: true },
                "cartItems": {},
                "qtyPurchased": 0,
                "valuePurchased": 0,
            });
            console.log('User Data saved Successfully');
            logger.info(response2, { structuredData: true });
            return { "success": true, "msg": response2 };
        } catch (error) {
            console.log("Failed to create user doc, need to delete this user again!");
            logger.info(error, { structuredData: true });
            await auth.deleteUser(response1.uid);
            console.log("User deleted successfully!");
            return { "success": false, "msg": error };
        }
    } catch (error) {
        logger.info(error, { structuredData: true });
        console.log('Error creating new user:', error);
        return { "success": false, "msg": error['errorInfo']['message'], "code": error['errorInfo']['code'] };
    }
});

exports.deleteUser = onCall(async (request) => {
    try {
      if (!request.auth) {
        return { status: 'error', code: 401, message: 'Not signed in' };
      }
  
      const uidToDelete = request.data.uid;
      if (!uidToDelete) {
        return { status: 'error', code: 400, message: 'No user UID provided' };
      }
  
      // First, delete Firestore user document
      try {
        await db.collection('users').doc(uidToDelete).delete();
        console.log(`User document ${uidToDelete} deleted successfully.`);
      } catch (firestoreError) {
        console.error(`Failed to delete user document ${uidToDelete}:`, firestoreError);
        return { success: false, msg: `Failed to delete user document: ${firestoreError.message}` };
      }
  
      // Then, delete Firebase Authentication user
      try {
        await auth.deleteUser(uidToDelete);
        console.log(`Firebase Auth user ${uidToDelete} deleted successfully.`);
        return { success: true, msg: `User ${uidToDelete} deleted successfully.` };
      } catch (authError) {
        console.error(`Failed to delete Auth user ${uidToDelete}:`, authError);
        return { success: false, msg: `Failed to delete auth user: ${authError.message}` };
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      return {
        success: false,
        msg: error.message || 'Unknown error',
        code: error.code || 500,
      };
    }
  });
  


// async function sendEmailToUser(to, subject, html) {
//     try {
//         const mailOptions = {
//             from: {
//                 name: 'WellFed',
//                 address: '<EMAIL>'
//             },
//             to: to,
//             subject: subject,
//             html: html
//         };
//         return transporter.sendMail(mailOptions, (error, data) => {
//             if (error) {
//                 console.log(error)
//                 return
//             }
//             console.log("Sent!")
//         });
//     } catch (error) {
//         console.log(error);
//     }
// }


// let final = {
//     "id": "evt_1NYY9XGp70OVnyZVS87XRVbB",
//     "object": "event",
//     "api_version": "2022-08-01",
//     "created": 1690479547,
//     "data": {
//         "object": {
//             "id": "cs_test_a1N6ZoXKfRpY3nRo2HGUTRYeKwa7qS3uxmBHVexRcGBvkFrbaSJ5JGJRiB",
//             "object": "checkout.session",
//             "after_expiration": null,
//             "allow_promotion_codes": null,
//             "amount_subtotal": 1999,
//             "amount_total": 1999,
//             "automatic_tax": {
//                 "enabled": false,
//                 "status": null
//             },
//             "billing_address_collection": null,
//             "cancel_url": "http://localhost:4242/cancel",
//             "client_reference_id": "ghkjPCt4UjthqIBBcQSE",
//             "consent": null,
//             "consent_collection": null,
//             "created": 1690479534,
//             "currency": "usd",
//             "currency_conversion": null,
//             "custom_fields": [

//             ],
//             "custom_text": {
//                 "shipping_address": null,
//                 "submit": null
//             },
//             "customer": null,
//             "customer_creation": "if_required",
//             "customer_details": {
//                 "address": {
//                     "city": null,
//                     "country": "IN",
//                     "line1": null,
//                     "line2": null,
//                     "postal_code": null,
//                     "state": null
//                 },
//                 "email": "<EMAIL>",
//                 "name": "Tyson Tyson",
//                 "phone": null,
//                 "tax_exempt": "none",
//                 "tax_ids": [

//                 ]
//             },
//             "customer_email": "<EMAIL>",
//             "expires_at": **********,
//             "invoice": null,
//             "invoice_creation": {
//                 "enabled": false,
//                 "invoice_data": {
//                     "account_tax_ids": null,
//                     "custom_fields": null,
//                     "description": null,
//                     "footer": null,
//                     "metadata": {
//                     },
//                     "rendering_options": null
//                 }
//             },
//             "livemode": false,
//             "locale": null,
//             "metadata": {
//             },
//             "mode": "payment",
//             "payment_intent": "pi_3NYY9WGp70OVnyZV00u5Sp5O",
//             "payment_link": null,
//             "payment_method_collection": "always",
//             "payment_method_options": {
//             },
//             "payment_method_types": [
//                 "card"
//             ],
//             "payment_status": "paid",
//             "phone_number_collection": {
//                 "enabled": false
//             },
//             "recovered_from": null,
//             "setup_intent": null,
//             "shipping_address_collection": null,
//             "shipping_cost": null,
//             "shipping_details": null,
//             "shipping_options": [

//             ],
//             "status": "complete",
//             "submit_type": null,
//             "subscription": null,
//             "success_url": "http://localhost:4242/success",
//             "total_details": {
//                 "amount_discount": 0,
//                 "amount_shipping": 0,
//                 "amount_tax": 0
//             },
//             "url": null
//         }
//     },
//     "livemode": false,
//     "pending_webhooks": 4,
//     "request": {
//         "id": null,
//         "idempotency_key": null
//     },
//     "type": "checkout.session.completed"
// };
